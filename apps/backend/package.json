{"name": "backend", "version": "0.1.0", "private": true, "description": "PillarScan Strapi Backend - Citizen Expression Platform", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "seed": "strapi console --run scripts/seed.js"}, "dependencies": {"@strapi/plugin-cloud": "5.15.1", "@strapi/plugin-users-permissions": "5.15.1", "@strapi/strapi": "5.15.1", "bcryptjs": "^2.4.3", "better-sqlite3": "^11.10.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "nodemailer": "^6.9.7", "pg": "^8.11.3", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "redis": "^4.6.10", "sharp": "^0.32.6", "socket.io": "^4.7.4", "styled-components": "^6.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.7", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "0c0aa88c-**************-47b534eff009", "installId": "b28e0df3e3e3c1da974f2d5d3ad3805070abc17375108be1cace31d8e5067d0b"}}