{"name": "frontend", "version": "0.1.0", "private": true, "description": "PillarScan Frontend - Citizen Expression Platform", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "@headlessui/react": "^2.0.4", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "clsx": "^2.0.0", "date-fns": "^3.0.6", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "mapbox-gl": "^3.0.1", "react-map-gl": "^7.1.7"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/mapbox-gl": "^3.1.0", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8", "eslint-config-next": "15.3.3"}}